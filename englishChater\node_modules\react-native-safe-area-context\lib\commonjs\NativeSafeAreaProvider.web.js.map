{"version": 3, "names": ["React", "_interopRequireWildcard", "require", "_reactNative", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "CSSTransitions", "WebkitTransition", "Transition", "MozTransition", "MSTransition", "OTransition", "NativeSafeAreaProvider", "children", "style", "onInsetsChange", "useEffect", "document", "element", "createContextElement", "body", "append<PERSON><PERSON><PERSON>", "onEnd", "paddingTop", "paddingBottom", "paddingLeft", "paddingRight", "window", "getComputedStyle", "insets", "top", "parseInt", "bottom", "left", "right", "frame", "x", "y", "width", "documentElement", "offsetWidth", "height", "offsetHeight", "nativeEvent", "addEventListener", "getSupportedTransitionEvent", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "createElement", "View", "_supportedTransitionEvent", "key", "undefined", "_supportedEnv", "getSupportedEnv", "CSS", "supports", "getInset", "side", "position", "zIndex", "overflow", "visibility", "transitionDuration", "transitionProperty", "transitionDelay"], "sourceRoot": "../../src", "sources": ["NativeSafeAreaProvider.web.tsx"], "mappings": ";;;;;;AAEA,IAAAA,KAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAAoC,SAAAE,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAJ,wBAAAI,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAHpC;;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,MAAMW,cAAsC,GAAG;EAC7CC,gBAAgB,EAAE,qBAAqB;EACvCC,UAAU,EAAE,eAAe;EAC3BC,aAAa,EAAE,eAAe;EAC9BC,YAAY,EAAE,iBAAiB;EAC/BC,WAAW,EAAE;AACf,CAAC;AAEM,SAASC,sBAAsBA,CAAC;EACrCC,QAAQ;EACRC,KAAK;EACLC;AAC2B,CAAC,EAAE;EAC9BjC,KAAK,CAACkC,SAAS,CAAC,MAAM;IACpB;IACA,IAAI,OAAOC,QAAQ,KAAK,WAAW,EAAE;MACnC;IACF;IAEA,MAAMC,OAAO,GAAGC,oBAAoB,CAAC,CAAC;IACtCF,QAAQ,CAACG,IAAI,CAACC,WAAW,CAACH,OAAO,CAAC;IAClC,MAAMI,KAAK,GAAGA,CAAA,KAAM;MAClB,MAAM;QAAEC,UAAU;QAAEC,aAAa;QAAEC,WAAW;QAAEC;MAAa,CAAC,GAC5DC,MAAM,CAACC,gBAAgB,CAACV,OAAO,CAAC;MAElC,MAAMW,MAAM,GAAG;QACbC,GAAG,EAAEP,UAAU,GAAGQ,QAAQ,CAACR,UAAU,EAAE,EAAE,CAAC,GAAG,CAAC;QAC9CS,MAAM,EAAER,aAAa,GAAGO,QAAQ,CAACP,aAAa,EAAE,EAAE,CAAC,GAAG,CAAC;QACvDS,IAAI,EAAER,WAAW,GAAGM,QAAQ,CAACN,WAAW,EAAE,EAAE,CAAC,GAAG,CAAC;QACjDS,KAAK,EAAER,YAAY,GAAGK,QAAQ,CAACL,YAAY,EAAE,EAAE,CAAC,GAAG;MACrD,CAAC;MACD,MAAMS,KAAK,GAAG;QACZC,CAAC,EAAE,CAAC;QACJC,CAAC,EAAE,CAAC;QACJC,KAAK,EAAErB,QAAQ,CAACsB,eAAe,CAACC,WAAW;QAC3CC,MAAM,EAAExB,QAAQ,CAACsB,eAAe,CAACG;MACnC,CAAC;MACD;MACA3B,cAAc,CAAC;QAAE4B,WAAW,EAAE;UAAEd,MAAM;UAAEM;QAAM;MAAE,CAAC,CAAC;IACpD,CAAC;IACDjB,OAAO,CAAC0B,gBAAgB,CAACC,2BAA2B,CAAC,CAAC,EAAEvB,KAAK,CAAC;IAC9DA,KAAK,CAAC,CAAC;IACP,OAAO,MAAM;MACXL,QAAQ,CAACG,IAAI,CAAC0B,WAAW,CAAC5B,OAAO,CAAC;MAClCA,OAAO,CAAC6B,mBAAmB,CAACF,2BAA2B,CAAC,CAAC,EAAEvB,KAAK,CAAC;IACnE,CAAC;EACH,CAAC,EAAE,CAACP,cAAc,CAAC,CAAC;EAEpB,oBAAOjC,KAAA,CAAAkE,aAAA,CAAC/D,YAAA,CAAAgE,IAAI;IAACnC,KAAK,EAAEA;EAAM,GAAED,QAAe,CAAC;AAC9C;AAEA,IAAIqC,yBAAoD,GAAG,IAAI;AAC/D,SAASL,2BAA2BA,CAAA,EAAW;EAC7C,IAAIK,yBAAyB,IAAI,IAAI,EAAE;IACrC,OAAOA,yBAAyB;EAClC;EACA,MAAMhC,OAAO,GAAGD,QAAQ,CAAC+B,aAAa,CAAC,aAAa,CAAC;EAErDE,yBAAyB,GAAG5C,cAAc,CAACE,UAAU;EACrD,KAAK,MAAM2C,GAAG,IAAI7C,cAAc,EAAE;IAChC,IAAIY,OAAO,CAACJ,KAAK,CAACqC,GAAG,CAA8B,KAAKC,SAAS,EAAE;MACjEF,yBAAyB,GAAG5C,cAAc,CAAC6C,GAAG,CAAC;MAC/C;IACF;EACF;EACA,OAAOD,yBAAyB;AAClC;AAIA,IAAIG,aAA4B,GAAG,IAAI;AACvC,SAASC,eAAeA,CAAA,EAAW;EACjC,IAAID,aAAa,KAAK,IAAI,EAAE;IAC1B,OAAOA,aAAa;EACtB;EACA,MAAM;IAAEE;EAAI,CAAC,GAAG5B,MAAM;EACtB,IACE4B,GAAG,IACHA,GAAG,CAACC,QAAQ,IACZD,GAAG,CAACC,QAAQ,CAAC,oCAAoC,CAAC,EAClD;IACAH,aAAa,GAAG,UAAU;EAC5B,CAAC,MAAM;IACLA,aAAa,GAAG,KAAK;EACvB;EACA,OAAOA,aAAa;AACtB;AAEA,SAASI,QAAQA,CAACC,IAAY,EAAU;EACtC,OAAO,GAAGJ,eAAe,CAAC,CAAC,oBAAoBI,IAAI,GAAG;AACxD;AAEA,SAASvC,oBAAoBA,CAAA,EAAgB;EAC3C,MAAMD,OAAO,GAAGD,QAAQ,CAAC+B,aAAa,CAAC,KAAK,CAAC;EAC7C,MAAM;IAAElC;EAAM,CAAC,GAAGI,OAAO;EACzBJ,KAAK,CAAC6C,QAAQ,GAAG,OAAO;EACxB7C,KAAK,CAACmB,IAAI,GAAG,GAAG;EAChBnB,KAAK,CAACgB,GAAG,GAAG,GAAG;EACfhB,KAAK,CAACwB,KAAK,GAAG,GAAG;EACjBxB,KAAK,CAAC2B,MAAM,GAAG,GAAG;EAClB3B,KAAK,CAAC8C,MAAM,GAAG,IAAI;EACnB9C,KAAK,CAAC+C,QAAQ,GAAG,QAAQ;EACzB/C,KAAK,CAACgD,UAAU,GAAG,QAAQ;EAC3B;EACAhD,KAAK,CAACiD,kBAAkB,GAAG,OAAO;EAClCjD,KAAK,CAACkD,kBAAkB,GAAG,SAAS;EACpClD,KAAK,CAACmD,eAAe,GAAG,IAAI;EAC5BnD,KAAK,CAACS,UAAU,GAAGkC,QAAQ,CAAC,KAAK,CAAC;EAClC3C,KAAK,CAACU,aAAa,GAAGiC,QAAQ,CAAC,QAAQ,CAAC;EACxC3C,KAAK,CAACW,WAAW,GAAGgC,QAAQ,CAAC,MAAM,CAAC;EACpC3C,KAAK,CAACY,YAAY,GAAG+B,QAAQ,CAAC,OAAO,CAAC;EACtC,OAAOvC,OAAO;AAChB", "ignoreList": []}
{"version": 3, "names": ["React", "useWindowDimensions", "View", "CompatNativeSafeAreaProvider", "children", "style", "onInsetsChange", "window", "useEffect", "insets", "top", "bottom", "left", "right", "frame", "x", "y", "width", "height", "nativeEvent", "createElement"], "sourceRoot": "../../src", "sources": ["CompatNativeSafeAreaProvider.tsx"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,mBAAmB,EAAEC,IAAI,QAAQ,cAAc;AAGxD,OAAO,SAASC,4BAA4BA,CAAC;EAC3CC,QAAQ;EACRC,KAAK;EACLC;AAC2B,CAAC,EAAE;EAC9B,MAAMC,MAAM,GAAGN,mBAAmB,CAAC,CAAC;EACpCD,KAAK,CAACQ,SAAS,CAAC,MAAM;IACpB,MAAMC,MAAM,GAAG;MACbC,GAAG,EAAE,CAAC;MACNC,MAAM,EAAE,CAAC;MACTC,IAAI,EAAE,CAAC;MACPC,KAAK,EAAE;IACT,CAAC;IACD,MAAMC,KAAK,GAAG;MACZC,CAAC,EAAE,CAAC;MACJC,CAAC,EAAE,CAAC;MACJC,KAAK,EAAEV,MAAM,CAACU,KAAK;MACnBC,MAAM,EAAEX,MAAM,CAACW;IACjB,CAAC;IACD;IACAZ,cAAc,CAAC;MAAEa,WAAW,EAAE;QAAEV,MAAM;QAAEK;MAAM;IAAE,CAAC,CAAC;EACpD,CAAC,EAAE,CAACR,cAAc,EAAEC,MAAM,CAACW,MAAM,EAAEX,MAAM,CAACU,KAAK,CAAC,CAAC;EACjD,oBAAOjB,KAAA,CAAAoB,aAAA,CAAClB,IAAI;IAACG,KAAK,EAAEA;EAAM,GAAED,QAAe,CAAC;AAC9C", "ignoreList": []}